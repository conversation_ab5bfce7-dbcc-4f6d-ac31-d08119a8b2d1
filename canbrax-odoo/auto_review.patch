diff --git a/canbrax_configmatrix/controllers/configuration_controller.py b/canbrax_configmatrix/controllers/configuration_controller.py
index 16d598b..0e2536e 100644
--- a/canbrax_configmatrix/controllers/configuration_controller.py
+++ b/canbrax_configmatrix/controllers/configuration_controller.py
@@ -399,8 +399,6 @@ class ConfigMatrixConfigurationController(http.Controller):
             operations = []
             total_cost = 0.0
 
-
-
             # Get field operation mappings through sections and fields
             for section in template.section_ids:
                 for field in section.field_ids:
@@ -421,20 +419,19 @@ class ConfigMatrixConfigurationController(http.Controller):
                             # In the future, we should have separate cost formulas
                             cost_value = duration_value
 
-                            if duration_value > 0:
-                                operation_data = {
-                                    'name': field.operation_name,
-                                    'workcenter': field.workcenter_id.name if field.workcenter_id else '',
-                                    'cost': cost_value,
-                                    'duration': duration_value,
-                                    'formula': field.duration_formula or '60.0',
-                                    'question_number': field.question_number if field.question_number else None,
-                                    'field_name': field.name,
-                                    'source_type': 'field_legacy'
-                                }
-
-                                operations.append(operation_data)
-                                total_cost += cost_value
+                            operation_data = {
+                                'name': field.operation_name,
+                                'workcenter': field.workcenter_id.name if field.workcenter_id else '',
+                                'cost': cost_value,
+                                'duration': duration_value,
+                                'formula': field.duration_formula or '60.0',
+                                'question_number': field.question_number if field.question_number else None,
+                                'field_name': field.name,
+                                'source_type': 'field_legacy'
+                            }
+
+                            operations.append(operation_data)
+                            total_cost += cost_value
                         except Exception as e:
                             _logger.warning(f"Error calculating cost for legacy field operation {field.operation_name}: {e}")
 
@@ -460,20 +457,19 @@ class ConfigMatrixConfigurationController(http.Controller):
                                 duration_value = mapping.default_duration
                                 cost_value = mapping.default_duration  # Fallback: assume cost equals duration
 
-                            if duration_value > 0:
-                                operation_data = {
-                                    'name': mapping.operation_name,
-                                    'workcenter': mapping.workcenter_id.name if mapping.workcenter_id else '',
-                                    'cost': cost_value,
-                                    'duration': duration_value,
-                                    'formula': mapping.duration_formula or 'Default value',
-                                    'question_number': field.question_number if field.question_number else None,
-                                    'field_name': field.name,
-                                    'source_type': 'field'
-                                }
+                            operation_data = {
+                                'name': mapping.operation_name,
+                                'workcenter': mapping.workcenter_id.name if mapping.workcenter_id else '',
+                                'cost': cost_value,
+                                'duration': duration_value,
+                                'formula': mapping.duration_formula or 'Default value',
+                                'question_number': field.question_number if field.question_number else None,
+                                'field_name': field.name,
+                                'source_type': 'field'
+                            }
 
-                                operations.append(operation_data)
-                                total_cost += cost_value
+                            operations.append(operation_data)
+                            total_cost += cost_value
 
                         except Exception as e:
                             _logger.warning(f"Error calculating cost for field operation {mapping.operation_name}: {e}")
@@ -501,21 +497,20 @@ class ConfigMatrixConfigurationController(http.Controller):
                                 # For legacy options, assume cost equals duration (backward compatibility)
                                 cost_value = duration_value
 
-                                if duration_value > 0:
-                                    operation_data = {
-                                        'name': option.operation_name,
-                                        'workcenter': option.workcenter_id.name if option.workcenter_id else '',
-                                        'cost': cost_value,
-                                        'duration': duration_value,
-                                        'formula': option.duration_formula or '60.0',
-                                        'question_number': field.question_number if field.question_number else None,
-                                        'field_name': field.name,
-                                        'option_name': option.name,
-                                        'source_type': 'option_legacy'
-                                    }
+                                operation_data = {
+                                    'name': option.operation_name,
+                                    'workcenter': option.workcenter_id.name if option.workcenter_id else '',
+                                    'cost': cost_value,
+                                    'duration': duration_value,
+                                    'formula': option.duration_formula or '60.0',
+                                    'question_number': field.question_number if field.question_number else None,
+                                    'field_name': field.name,
+                                    'option_name': option.name,
+                                    'source_type': 'option_legacy'
+                                }
 
-                                    operations.append(operation_data)
-                                    total_cost += cost_value
+                                operations.append(operation_data)
+                                total_cost += cost_value
                             except Exception as e:
                                 _logger.warning(f"Error calculating cost for legacy option operation {option.operation_name}: {e}")
 
