<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Base SVG Component for 1100x620 Cut Plan -->
        <record id="svg_base_1100x620" model="mesh.cut.plan.svg.component">
            <field name="name">Base - 1100x620 SWS</field>
            <field name="cut_plan_id" ref="mesh_cut_plan_1100x620"/>
            <field name="component_type">base</field>
            <field name="z_index">1</field>
            <field name="svg_content"><![CDATA[<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 800 600" width="100%" height="100%">
  <!-- Grid background -->
  <defs>
    <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
      <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#e0e0e0" stroke-width="1"/>
    </pattern>
  </defs>
  <rect width="100%" height="100%" fill="url(#grid)"/>

  <!-- Title -->
  <rect x="0" y="0" width="800" height="60" fill="#87CEEB"/>
  <text x="400" y="35" text-anchor="middle" font-size="24" font-weight="bold" fill="#000">
    1100 x 620 SWS
  </text>

  <!-- Master sheet outline (1100x620 scaled to fit) -->
  <rect x="150" y="100" width="500" height="280"
        fill="none" stroke="#333" stroke-width="3"/>

  <!-- Dimensions -->
  <text x="400" y="420" text-anchor="middle" font-size="16" fill="#666">
    Master Sheet: 1100 x 620 mm
  </text>

  <!-- Left dimension -->
  <text x="120" y="250" text-anchor="middle" font-size="14" fill="#666" transform="rotate(-90 120 250)">
    620
  </text>

  <!-- Bottom dimension -->
  <text x="400" y="400" text-anchor="middle" font-size="14" fill="#666">
    1100
  </text>
</svg>]]></field>
        </record>

        <!-- Cut Lines Layer for 1100x620 -->
        <record id="svg_cut_lines_1100x620" model="mesh.cut.plan.svg.component">
            <field name="name">Cut Lines - 1100x620</field>
            <field name="cut_plan_id" ref="mesh_cut_plan_1100x620"/>
            <field name="component_type">layer</field>
            <field name="z_index">10</field>
            <field name="svg_content"><![CDATA[<!-- Cut lines for 1100x620 plan -->
<g id="cut-lines">
  <!-- First cut (horizontal) -->
  <line x1="150" y1="240" x2="650" y2="240" stroke="#FF1493" stroke-width="3" stroke-dasharray="5,5"/>
  <text x="400" y="235" text-anchor="middle" font-size="12" fill="#FF1493" font-weight="bold">1st Cut</text>
  
  <!-- Second cut (vertical) -->
  <line x1="470" y1="240" x2="470" y2="380" stroke="#FF8C00" stroke-width="3" stroke-dasharray="5,5"/>
  <text x="475" y="310" font-size="12" fill="#FF8C00" font-weight="bold">2nd Cut</text>
</g>]]></field>
        </record>

        <!-- Planned Off-cuts Layer for 1100x620 -->
        <record id="svg_offcuts_1100x620" model="mesh.cut.plan.svg.component">
            <field name="name">Planned Off-cuts - 1100x620</field>
            <field name="cut_plan_id" ref="mesh_cut_plan_1100x620"/>
            <field name="component_type">layer</field>
            <field name="z_index">20</field>
            <field name="svg_content"><![CDATA[<!-- Planned off-cuts for 1100x620 -->
<g id="planned-offcuts">
  <!-- Top sections (6x12 Plan O/C) -->
  <ellipse cx="250" cy="170" rx="80" ry="30" fill="none" stroke="#9370DB" stroke-width="2"/>
  <text x="250" y="165" text-anchor="middle" font-size="10" fill="#9370DB">6 x 12</text>
  <text x="250" y="175" text-anchor="middle" font-size="10" fill="#9370DB">Plan O/C</text>
  <text x="200" y="150" font-size="8" fill="#9370DB">1/2 of O/C</text>
  
  <ellipse cx="550" cy="170" rx="80" ry="30" fill="none" stroke="#9370DB" stroke-width="2"/>
  <text x="550" y="165" text-anchor="middle" font-size="10" fill="#9370DB">6 x 12</text>
  <text x="550" y="175" text-anchor="middle" font-size="10" fill="#9370DB">Plan O/C</text>
  <text x="500" y="150" font-size="8" fill="#9370DB">1/2 of O/C</text>
  
  <!-- Bottom left section -->
  <text x="310" y="320" text-anchor="middle" font-size="16" fill="#FF1493" font-weight="bold">620</text>
  
  <!-- Bottom right section -->
  <text x="560" y="320" text-anchor="middle" font-size="16" fill="#FF8C00" font-weight="bold">1100</text>
</g>]]></field>
        </record>

        <!-- Base SVG Component for 1250x1000 Cut Plan -->
        <record id="svg_base_1250x1000" model="mesh.cut.plan.svg.component">
            <field name="name">Base - 1250x1000 SWS</field>
            <field name="cut_plan_id" ref="mesh_cut_plan_1250x1000"/>
            <field name="component_type">base</field>
            <field name="z_index">1</field>
            <field name="svg_content"><![CDATA[<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 800 600" width="100%" height="100%">
  <!-- Grid background -->
  <defs>
    <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
      <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#e0e0e0" stroke-width="1"/>
    </pattern>
  </defs>
  <rect width="100%" height="100%" fill="url(#grid)"/>
  
  <!-- Title -->
  <rect x="0" y="0" width="800" height="60" fill="#87CEEB"/>
  <text x="400" y="35" text-anchor="middle" font-size="24" font-weight="bold" fill="#000">
    1250 x 1000 SWS
  </text>

  <!-- Master sheet outline (1250x1000 scaled to fit) -->
  <rect x="100" y="80" width="600" height="480"
        fill="none" stroke="#333" stroke-width="3"/>

  <!-- Dimensions -->
  <text x="400" y="580" text-anchor="middle" font-size="16" fill="#666">
    Master Sheet: 1250 x 1000 mm
  </text>

  <!-- Left dimension -->
  <text x="70" y="320" text-anchor="middle" font-size="14" fill="#666" transform="rotate(-90 70 320)">
    1000
  </text>

  <!-- Bottom dimension -->
  <text x="400" y="575" text-anchor="middle" font-size="14" fill="#666">
    1250
  </text>
</svg>]]></field>
        </record>

        <!-- Cut Lines Layer for 1250x1000 -->
        <record id="svg_cut_lines_1250x1000" model="mesh.cut.plan.svg.component">
            <field name="name">Cut Lines - 1250x1000</field>
            <field name="cut_plan_id" ref="mesh_cut_plan_1250x1000"/>
            <field name="component_type">layer</field>
            <field name="z_index">10</field>
            <field name="svg_content"><![CDATA[<!-- Cut lines for 1250x1000 plan -->
<g id="cut-lines">
  <!-- Multiple cut lines for larger sheet -->
  <line x1="100" y1="200" x2="700" y2="200" stroke="#FF1493" stroke-width="2" stroke-dasharray="5,5"/>
  <text x="400" y="195" text-anchor="middle" font-size="10" fill="#FF1493">Cut 1</text>
  
  <line x1="100" y1="320" x2="700" y2="320" stroke="#FF8C00" stroke-width="2" stroke-dasharray="5,5"/>
  <text x="400" y="315" text-anchor="middle" font-size="10" fill="#FF8C00">Cut 2</text>
  
  <line x1="100" y1="440" x2="700" y2="440" stroke="#32CD32" stroke-width="2" stroke-dasharray="5,5"/>
  <text x="400" y="435" text-anchor="middle" font-size="10" fill="#32CD32">Cut 3</text>
  
  <!-- Vertical cuts -->
  <line x1="300" y1="80" x2="300" y2="560" stroke="#9370DB" stroke-width="2" stroke-dasharray="3,3"/>
  <line x1="500" y1="80" x2="500" y2="560" stroke="#9370DB" stroke-width="2" stroke-dasharray="3,3"/>
</g>]]></field>
        </record>

        <!-- Planned Off-cuts Layer for 1250x1000 -->
        <record id="svg_offcuts_1250x1000" model="mesh.cut.plan.svg.component">
            <field name="name">Planned Off-cuts - 1250x1000</field>
            <field name="cut_plan_id" ref="mesh_cut_plan_1250x1000"/>
            <field name="component_type">layer</field>
            <field name="z_index">20</field>
            <field name="svg_content"><![CDATA[<!-- Planned off-cuts for 1250x1000 -->
<g id="planned-offcuts">
  <!-- Multiple planned sections -->
  <rect x="120" y="100" width="160" height="80" fill="rgba(147, 112, 219, 0.2)" stroke="#9370DB" stroke-width="1"/>
  <text x="200" y="140" text-anchor="middle" font-size="12" fill="#9370DB" font-weight="bold">600x400</text>
  
  <rect x="320" y="100" width="160" height="80" fill="rgba(255, 20, 147, 0.2)" stroke="#FF1493" stroke-width="1"/>
  <text x="400" y="140" text-anchor="middle" font-size="12" fill="#FF1493" font-weight="bold">650x400</text>
  
  <rect x="520" y="100" width="160" height="80" fill="rgba(50, 205, 50, 0.2)" stroke="#32CD32" stroke-width="1"/>
  <text x="600" y="140" text-anchor="middle" font-size="12" fill="#32CD32" font-weight="bold">600x400</text>
  
  <!-- Bottom sections -->
  <rect x="200" y="460" width="180" height="80" fill="rgba(255, 140, 0, 0.2)" stroke="#FF8C00" stroke-width="1"/>
  <text x="290" y="500" text-anchor="middle" font-size="12" fill="#FF8C00" font-weight="bold">Waste Area</text>
</g>]]></field>
        </record>

    </data>
</odoo>
