# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
import re
from markupsafe import Markup

class MeshCutPlanSvgComponent(models.Model):
    _name = 'mesh.cut.plan.svg.component'
    _description = 'SVG Component for Cut Plan Visualization'
    _order = 'z_index'

    name = fields.Char("Component Name", required=True)
    cut_plan_id = fields.Many2one('mesh.cut.plan', "Cut Plan", required=True, ondelete='cascade')

    # Component type
    component_type = fields.Selection([
        ('base', 'Base SVG (Background)'),
        ('layer', 'SVG Layer (Conditional)'),
    ], required=True, default='layer')

    # SVG content
    svg_content = fields.Text("SVG Content", required=True,
        help="SVG markup for this component. Use ${field_name} for values from cut plan.")

    # Visibility condition
    condition = fields.Char("Visibility Condition",
        help="JavaScript condition that determines if this component is included. Leave empty for always visible.")

    # Positioning and styling
    z_index = fields.Integer("Z-Index", default=10,
        help="Display order - higher numbers are displayed on top")

    # SVG Previews
    svg_preview = fields.Html("SVG Preview", compute='_compute_svg_preview', sanitize=False)
    combined_preview = fields.Html("Combined Preview", compute='_compute_combined_preview', sanitize=False)

    @api.model
    def create(self, vals):
        """Clean SVG content before saving"""
        if 'svg_content' in vals:
            vals['svg_content'] = self._clean_svg_content(vals['svg_content'])
        return super(MeshCutPlanSvgComponent, self).create(vals)

    def write(self, vals):
        """Clean SVG content before saving"""
        if 'svg_content' in vals:
            vals['svg_content'] = self._clean_svg_content(vals['svg_content'])
        return super(MeshCutPlanSvgComponent, self).write(vals)

    def _clean_svg_content(self, content):
        """Clean SVG content to ensure it's valid and safe"""
        if not content:
            return content

        # Remove XML declaration if present
        content = re.sub(r'<\?xml[^>]*\?>', '', content)

        # Remove DOCTYPE if present
        content = re.sub(r'<!DOCTYPE[^>]*>', '', content)

        # Ensure SVG tag has proper namespace
        if '<svg' in content and 'xmlns=' not in content:
            content = content.replace('<svg', '<svg xmlns="http://www.w3.org/2000/svg"')

        # Remove comments
        content = re.sub(r'<!--.*?-->', '', content, flags=re.DOTALL)

        return content

    def _process_template_variables(self, content):
        """Process template variables in SVG content"""
        if not content or not self.cut_plan_id:
            return content

        import re

        # Replace ${field_name} with actual values from cut plan
        def replace_var(match):
            field_name = match.group(1)
            if hasattr(self.cut_plan_id, field_name):
                value = getattr(self.cut_plan_id, field_name)
                return str(value) if value is not None else ''
            return match.group(0)  # Keep original if field not found

        return re.sub(r'\$\{([^}]+)\}', replace_var, content)

    @api.depends('svg_content', 'component_type', 'cut_plan_id')
    def _compute_svg_preview(self):
        """Generate a preview of the SVG component"""
        for component in self:
            if not component.svg_content:
                component.svg_preview = '<div class="alert alert-warning">No SVG content</div>'
                continue

            try:
                content = component.svg_content

                # Process template variables if cut plan is available
                if component.cut_plan_id:
                    content = component._process_template_variables(content)

                # For layer components, wrap in an SVG tag for preview
                if component.component_type == 'layer':
                    content = f'''
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 800 600" width="100%" height="100%">
                        {content}
                    </svg>
                    '''

                # Set the preview - use Markup to prevent HTML escaping
                component.svg_preview = Markup(f'''
                <div style="display: flex; justify-content: center; align-items: center; height: 100%;">
                    {content}
                </div>
                ''')
            except Exception as e:
                component.svg_preview = f'<div class="alert alert-danger">Error rendering SVG: {str(e)}</div>'

    @api.depends('svg_content', 'component_type', 'cut_plan_id', 'z_index')
    def _compute_combined_preview(self):
        """Generate a preview of the SVG component combined with other components"""
        for component in self:
            if not component.svg_content or not component.cut_plan_id:
                component.combined_preview = '<div class="alert alert-warning">No SVG content or cut plan</div>'
                continue

            try:
                # Get all components for this cut plan, ordered by z_index
                all_components = self.search([
                    ('cut_plan_id', '=', component.cut_plan_id.id)
                ], order='z_index')

                # Find base component
                base_component = all_components.filtered(lambda c: c.component_type == 'base')
                if not base_component:
                    component.combined_preview = '<div class="alert alert-warning">No base SVG component found</div>'
                    continue

                base_component = base_component[0]

                # Start with base SVG content
                combined_content = base_component._process_template_variables(base_component.svg_content)

                # Add other layers
                current_added = False
                for comp in all_components.filtered(lambda c: c.component_type == 'layer'):
                    if comp.id == component.id:
                        current_added = True

                    # Process template variables and add layer content before closing </svg> tag
                    layer_content = comp._process_template_variables(comp.svg_content)
                    combined_content = combined_content.replace('</svg>', layer_content + '</svg>')

                # Add current component at the end if it hasn't been added yet and it's a layer
                if component.component_type == 'layer' and not current_added:
                    current_content = component._process_template_variables(component.svg_content)
                    combined_content = combined_content.replace('</svg>', current_content + '</svg>')

                    # Add a highlight to show this component's position
                    highlight = f'''
                    <rect x="0" y="0" width="100%" height="100%"
                          fill="none" stroke="#FF5722" stroke-width="3" stroke-dasharray="5,5"/>
                    <text x="50%" y="20" text-anchor="middle" font-size="14" fill="#FF5722" font-weight="bold">
                        This Layer (Z-Index: {component.z_index})
                    </text>
                    '''
                    combined_content = combined_content.replace('</svg>', highlight + '</svg>')

                # Set the preview
                component.combined_preview = Markup(f'''
                <div style="display: flex; justify-content: center; align-items: center; height: 100%;">
                    {combined_content}
                </div>
                ''')

            except Exception as e:
                component.combined_preview = f'<div class="alert alert-danger">Error rendering combined SVG: {str(e)}</div>'

    def action_preview_svg(self):
        """Action to preview the SVG component"""
        return {
            'type': 'ir.actions.act_window',
            'name': f'Preview: {self.name}',
            'res_model': 'mesh.cut.plan.svg.component',
            'res_id': self.id,
            'view_mode': 'form',
            'view_id': self.env.ref('canbrax_configmatrix.view_mesh_cut_plan_svg_component_preview_form').id,
            'target': 'new',
        }
