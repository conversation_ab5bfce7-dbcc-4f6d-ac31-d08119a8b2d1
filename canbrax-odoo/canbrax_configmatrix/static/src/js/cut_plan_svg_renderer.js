/**
 * Cut Plan SVG Renderer
 * 
 * This module provides SVG rendering functionality for Cut Plans,
 * similar to the configurator's SVG system but adapted for cut plan visualization.
 */

odoo.define('canbrax_configmatrix.cut_plan_svg_renderer', function (require) {
    'use strict';

    var Widget = require('web.Widget');
    var core = require('web.core');
    var rpc = require('web.rpc');

    var CutPlanSvgRenderer = Widget.extend({
        template: 'CutPlanSvgRenderer',
        
        init: function (parent, options) {
            this._super.apply(this, arguments);
            this.cut_plan_id = options.cut_plan_id || null;
            this.components = [];
            this.planValues = {};
        },

        start: function () {
            console.log('Cut Plan SVG Renderer start');
            this.svgContainer = this.$el.find('.svg-container');
            if (this.cut_plan_id) {
                this._loadComponents();
            } else {
                this._showError('No cut plan ID provided');
            }
            return this._super.apply(this, arguments);
        },

        _loadComponents: function () {
            var self = this;
            console.log('Loading SVG components for cut plan ID:', this.cut_plan_id);
            
            return rpc.query({
                model: 'mesh.cut.plan',
                method: 'get_svg_components',
                args: [this.cut_plan_id],
            }).then(function(result) {
                console.log('SVG components response:', result);

                if (result.success) {
                    self.components = result.components || [];
                    self.planValues = result.plan_values || {};
                    console.log('Loaded', self.components.length, 'SVG components');
                    self._renderSvg();
                } else {
                    console.error("Failed to load SVG components:", result.error);
                    self._showError(result.error || 'Failed to load SVG components');
                }
            }).catch(function(error) {
                console.error('AJAX error loading SVG components:', error);
                self._showError('Failed to load SVG components');
            });
        },

        updatePlanValues: function (values) {
            console.log('Updating SVG with plan values:', values);
            this.planValues = values || {};
            this._renderSvg();
        },

        _renderSvg: function () {
            console.log('Rendering SVG with', this.components.length, 'components');

            if (!this.svgContainer) {
                console.error('SVG container not found');
                return;
            }

            if (this.components.length === 0) {
                console.warn('No SVG components available');
                this._showError('No SVG components available');
                return;
            }

            // Find base component
            var baseComponent = this.components.find(function(comp) {
                return comp.component_type === 'base';
            });

            if (!baseComponent) {
                console.error("No base SVG component found");
                this._showError('No base SVG component found');
                return;
            }

            console.log('Using base component:', baseComponent.name);

            // Start with base SVG content
            var svgContent = this._processTemplate(baseComponent.svg_content);

            // Add other layers based on conditions
            var addedLayers = 0;
            this.components.forEach(function(component) {
                if (component.component_type !== 'layer') {
                    return;
                }

                // Evaluate condition if present
                if (component.condition) {
                    var conditionResult = this._evaluateCondition(component.condition);
                    console.log('Evaluating condition for', component.name, ':', component.condition, '=', conditionResult);
                    if (!conditionResult) {
                        return;
                    }
                }

                console.log('Adding layer:', component.name);

                // Process template with current plan values
                var layerSvg = this._processTemplate(component.svg_content);

                // Add to SVG content - before the closing </svg> tag
                svgContent = svgContent.replace('</svg>', layerSvg + '</svg>');
                addedLayers++;
            }, this);

            console.log('Added', addedLayers, 'layers to SVG');

            // Update the SVG display
            try {
                var tempDiv = document.createElement('div');
                tempDiv.innerHTML = svgContent;

                this.svgContainer.empty();

                if (tempDiv.firstChild) {
                    this.svgContainer.append(tempDiv.firstChild);
                    console.log('SVG rendered successfully');
                } else {
                    console.error('Failed to parse SVG content');
                    this._showError('Failed to parse SVG content');
                }
            } catch (e) {
                console.error('Error rendering SVG:', e);
                this._showError('Error rendering SVG');
            }
        },

        _processTemplate: function (template) {
            if (!template) return '';
            
            var processed = template;
            var self = this;
            
            // Replace ${field_name} with actual values
            processed = processed.replace(/\$\{([^}]+)\}/g, function(match, fieldName) {
                var value = self.planValues[fieldName];
                if (value !== undefined) {
                    return value;
                }
                console.warn('Template field not found:', fieldName);
                return match; // Keep original if not found
            });
            
            return processed;
        },

        _evaluateCondition: function (condition) {
            if (!condition) return true;
            
            try {
                // Extract field names from the condition
                var fieldNames = condition.match(/\b[a-zA-Z_][a-zA-Z0-9_]*\b/g) || [];
                
                // Create a context with plan values
                var context = Object.assign({}, this.planValues);
                
                // Add missing fields with default values
                fieldNames.forEach(function(fieldName) {
                    if (context[fieldName] === undefined) {
                        context[fieldName] = '';
                    }
                });
                
                // Create a function with the field values in scope
                var func = new Function(...Object.keys(context), `return ${condition};`);
                
                // Call the function with the field values
                return func(...Object.values(context));
            } catch (e) {
                console.error('Error evaluating condition:', e, 'with values:', this.planValues);
                return false;
            }
        },

        _showError: function (message) {
            if (this.svgContainer) {
                this.svgContainer.html('<div class="alert alert-warning">' + message + '</div>');
            }
        },
    });

    return CutPlanSvgRenderer;
});
