/**
 * Cut Plan SVG Widget
 * 
 * A field widget that displays SVG visualization for cut plans
 */

odoo.define('canbrax_configmatrix.cut_plan_svg_widget', function (require) {
    'use strict';

    var AbstractField = require('web.AbstractField');
    var field_registry = require('web.field_registry');
    var CutPlanSvgRenderer = require('canbrax_configmatrix.cut_plan_svg_renderer');

    var CutPlanSvgWidget = AbstractField.extend({
        className: 'o_field_cut_plan_svg',
        supportedFieldTypes: ['char', 'text'],

        init: function () {
            this._super.apply(this, arguments);
            this.svgRenderer = null;
        },

        start: function () {
            var self = this;
            return this._super.apply(this, arguments).then(function () {
                self._renderSvg();
            });
        },

        _renderSvg: function () {
            var self = this;
            
            // Get the cut plan ID from the record
            var cutPlanId = this.record.data.id;
            
            if (!cutPlanId) {
                this.$el.html('<div class="alert alert-warning">Save the cut plan first to see visualization</div>');
                return;
            }

            // Create the SVG renderer
            if (this.svgRenderer) {
                this.svgRenderer.destroy();
            }

            this.svgRenderer = new CutPlanSvgRenderer(this, {
                cut_plan_id: cutPlanId
            });

            // Render the SVG
            this.svgRenderer.appendTo(this.$el);
        },

        _render: function () {
            // Re-render when the field is updated
            this._renderSvg();
        },

        destroy: function () {
            if (this.svgRenderer) {
                this.svgRenderer.destroy();
            }
            this._super.apply(this, arguments);
        },
    });

    // Register the widget
    field_registry.add('cut_plan_svg', CutPlanSvgWidget);

    return CutPlanSvgWidget;
});
