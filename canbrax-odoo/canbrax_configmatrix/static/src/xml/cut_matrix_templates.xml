<?xml version="1.0" encoding="UTF-8"?>
<templates>
    <t t-name="canbrax_configmatrix.CutMatrixEditor" owl="1">
        <div class="excel-matrix-editor" t-ref="matrixGrid">

            <!-- Toolbar -->
            <div class="matrix-toolbar">
                <div class="toolbar-left">
                    <button type="button"
                            class="btn btn-sm btn-outline-primary"
                            t-on-click="importExcelMatrixData">
                        <i class="fa fa-file-excel-o"></i> Import Excel Data
                    </button>
                    <button type="button"
                            class="btn btn-sm btn-outline-secondary"
                            t-on-click="fillSampleData">
                        <i class="fa fa-magic"></i> Sample Data
                    </button>
                    <button type="button"
                            class="btn btn-sm btn-outline-info"
                            t-on-click="exportMatrixData">
                        <i class="fa fa-download"></i> Export Data
                    </button>
                    <button type="button"
                            class="btn btn-sm btn-outline-warning"
                            t-on-click="clearMatrix">
                        <i class="fa fa-trash"></i> Clear Matrix
                    </button>
                </div>

                <div class="toolbar-right">
                    <div class="matrix-stats">
                        <span class="badge badge-info">
                            <t t-esc="state.stats.filled"/>/<t t-esc="state.stats.total"/> cells
                        </span>
                        <span class="badge badge-success">
                            <t t-esc="state.stats.completion"/>% complete
                        </span>
                    </div>
                </div>
            </div>

            <!-- Excel-style Matrix Grid -->
            <div class="excel-matrix-container">
                <div t-if="state.heights.length === 0 or state.widths.length === 0"
                     class="alert alert-warning">
                    <i class="fa fa-exclamation-triangle"></i>
                    Please configure height and width values first in the Configuration tab.
                </div>

                <div t-else="" class="excel-grid-wrapper">
                    <!-- Column Headers (A, B, C, etc.) -->
                    <div class="excel-column-headers">
                        <div class="excel-corner-cell"></div>
                        <div t-foreach="state.widths" t-as="width" t-key="width"
                             class="excel-column-header"
                             t-att-data-width="width">
                            <div class="column-letter"><t t-esc="getColumnLetter(width_index)"/></div>
                            <div class="column-value"><t t-esc="width"/></div>
                        </div>
                    </div>

                    <!-- Matrix Rows -->
                    <div class="excel-matrix-rows">
                        <div t-foreach="state.heights" t-as="height" t-key="height" class="excel-matrix-row">
                            <!-- Row Header -->
                            <div class="excel-row-header" t-att-data-height="height">
                                <div class="row-number"><t t-esc="height_index + 1"/></div>
                                <div class="row-value"><t t-esc="height"/></div>
                            </div>

                            <!-- Row Cells -->
                            <div t-foreach="state.widths" t-as="width" t-key="width"
                                 class="excel-matrix-cell"
                                 t-att-class="getExcelCellClass(height, width)"
                                 t-att-data-height="height"
                                 t-att-data-width="width"
                                 t-att-data-cell="getColumnLetter(width_index) + (height_index + 1)"
                                 t-on-click="() => this.onCellClick(height, width)"
                                 t-on-contextmenu="(event) => this.onCellRightClick(event, height, width)"
                                 t-att-title="`Cell ${getColumnLetter(width_index)}${height_index + 1}: ${height}x${width}mm - Click to assign cut plan`">

                                <div class="excel-cell-content" t-out="getExcelCellContent(height, width)"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Legend -->
            <div class="matrix-legend mt-3">
                <div class="row">
                    <div class="col-md-6">
                        <h6 style="color: #333; font-weight: bold;">Cut Plan Types:</h6>
                        <ul class="list-unstyled" style="color: #333;">
                            <li style="color: #333; margin-bottom: 5px;"><span class="legend-color" style="background-color: #fff; border: 1px solid #ddd;"></span> No cut plan configured</li>
                            <li style="color: #333; margin-bottom: 5px;"><span class="legend-color" style="background-color: #e8f5e8; border: 1px solid #4caf50;"></span> Standard cut (same size sheet)</li>
                            <li style="color: #333; margin-bottom: 5px;"><span class="legend-color" style="background-color: #fff3e0; border: 1px solid #ff9800;"></span> Custom cut plan with master sheet</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 style="color: #333; font-weight: bold;">Instructions:</h6>
                        <ul class="list-unstyled" style="color: #333;">
                            <li style="color: #333; margin-bottom: 3px;">1. Click on any cell to configure its cut plan</li>
                            <li style="color: #333; margin-bottom: 3px;">2. Select from predefined cut plans or create custom ones</li>
                            <li style="color: #333; margin-bottom: 3px;">3. Cut plans define master sheet size and cutting sequence</li>
                            <li style="color: #333; margin-bottom: 3px;">4. Changes are saved automatically</li>
                        </ul>
                    </div>
                </div>
            </div>



            <!-- Instructions for direct cell interaction -->
            <div class="matrix-instructions">
                <p><strong>Click cells to cycle through:</strong> Empty → Arrow Right (→) → Arrow Down (↓) → Choose Cut Plan → Empty</p>
                <p><strong>Right-click</strong> to cycle backwards through the states</p>
            </div>

            <!-- Cut Plan Selection Dialog -->
            <div t-if="state.showCutPlanDialog" class="cut-matrix-modal-overlay" t-on-click="closeCutPlanDialog">
                <div class="cut-matrix-modal-dialog" t-on-click.stop="">
                    <div class="cut-matrix-modal-header">
                        <h5><i class="fa fa-scissors"></i> Choose Cut Plan</h5>
                        <button type="button" class="btn-close" t-on-click="closeCutPlanDialog" title="Close">×</button>
                    </div>
                    <div class="cut-matrix-modal-body">
                        <div t-if="state.selectedCellForPlan">
                            <div class="cell-info mb-3">
                                <h6><i class="fa fa-th-large"></i> Cell: <t t-esc="state.selectedCellForPlan.height"/>×<t t-esc="state.selectedCellForPlan.width"/>mm</h6>
                            </div>

                            <!-- Current Assignment Section -->
                            <div t-if="state.matrixData[state.selectedCellForPlan.key] and state.matrixData[state.selectedCellForPlan.key].has_cut_plan" class="current-assignment mb-3">
                                <div class="alert alert-success">
                                    <h6><i class="fa fa-check-circle"></i> Currently Assigned</h6>
                                    <div class="current-plan-details">
                                        <div><strong>Cut Plan:</strong> <t t-esc="state.matrixData[state.selectedCellForPlan.key].cut_plan_name"/></div>
                                        <div><strong>Master Sheet:</strong> <t t-esc="state.matrixData[state.selectedCellForPlan.key].master_sheet_size"/>mm</div>
                                        <div t-if="state.matrixData[state.selectedCellForPlan.key].efficiency">
                                            <strong>Efficiency:</strong>
                                            <span t-att-class="`badge ${state.matrixData[state.selectedCellForPlan.key].efficiency >= 0.8 ? 'badge-success' : state.matrixData[state.selectedCellForPlan.key].efficiency >= 0.6 ? 'badge-warning' : 'badge-danger'}`">
                                                <t t-esc="Math.round(state.matrixData[state.selectedCellForPlan.key].efficiency * 100)"/>%
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Cut Plans Section -->
                            <div t-if="state.availableCutPlans.length > 0">
                                <h6>
                                    <i class="fa fa-scissors"></i>
                                    <span t-if="state.matrixData[state.selectedCellForPlan.key] and state.matrixData[state.selectedCellForPlan.key].has_cut_plan">
                                        Change to Different Cut Plan (<t t-esc="state.availableCutPlans.length"/>)
                                    </span>
                                    <span t-else="">
                                        Available Cut Plans (<t t-esc="state.availableCutPlans.length"/>)
                                    </span>
                                </h6>
                                <div class="cut-plan-list">
                                    <div t-foreach="state.availableCutPlans" t-as="plan" t-key="plan.id"
                                         t-attf-class="cut-plan-option {{ isCutPlanSelected(plan.id) ? 'selected' : '' }}"
                                         t-on-click="() => this.toggleCutPlanSelection(plan.id)">
                                        <div class="plan-header">
                                            <div class="plan-checkbox">
                                                <input type="checkbox"
                                                       t-att-checked="isCutPlanSelected(plan.id)"
                                                       t-on-click.stop=""
                                                       readonly="readonly"/>
                                            </div>
                                            <div class="plan-name">
                                                <i class="fa fa-file-text-o"></i>
                                                <strong><t t-esc="plan.name"/></strong>
                                            </div>
                                            <span class="efficiency-badge" t-att-class="`badge ${plan.efficiency >= 0.8 ? 'badge-success' : plan.efficiency >= 0.6 ? 'badge-warning' : 'badge-danger'}`">
                                                <t t-esc="Math.round(plan.efficiency * 100)"/>% efficient
                                            </span>
                                        </div>
                                        <div class="plan-details">
                                            <div class="detail-row">
                                                <span><i class="fa fa-expand"></i> Master Sheet: <strong><t t-esc="plan.master_width"/>×<t t-esc="plan.master_height"/>mm</strong></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div t-else="" class="alert alert-info">
                                <i class="fa fa-info-circle"></i>
                                <strong>No cut plans available</strong><br/>
                                <t t-if="state.noCutPlansMessage" t-esc="state.noCutPlansMessage"/>
                                <t t-else="">
                                    No cut plans can fit this cell size (<t t-esc="state.selectedCellForPlan.height"/>×<t t-esc="state.selectedCellForPlan.width"/>mm).<br/>
                                    Go to the <strong>"Cut Plans"</strong> tab to create cut plans with master sheets larger than this size.
                                </t>
                            </div>

                            <!-- Cut to Size Option -->
                            <div class="cut-to-size-section">
                                <h6 class="section-title">
                                    <i class="fa fa-scissors"></i> Alternative Option
                                </h6>
                                <div class="cut-to-size-option" t-on-click="() => this.assignCutToSizeToCell(state.selectedCellForPlan.height, state.selectedCellForPlan.width)">
                                    <div class="plan-header">
                                        <div class="plan-name">
                                            <i class="fa fa-scissors"></i>
                                            <strong>Cut to Size</strong>
                                        </div>
                                        <div class="plan-description">
                                            Cut mesh to exact dimensions: <t t-esc="state.selectedCellForPlan.height"/>×<t t-esc="state.selectedCellForPlan.width"/>mm
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="cut-matrix-modal-footer">
                        <div class="footer-left">
                            <button type="button" class="btn btn-outline-danger" t-on-click="removeCutPlanFromCell">
                                <i class="fa fa-eraser"></i> Clear Cell
                            </button>
                        </div>
                        <div class="footer-center">
                            <span class="selection-info" t-if="state.selectedCutPlanIds.length > 0">
                                <t t-esc="state.selectedCutPlanIds.length"/> of 2 selected
                            </span>
                        </div>
                        <div class="footer-right">
                            <button type="button" class="btn btn-warning"
                                    t-on-click="() => this.assignCutToSizeToCell(state.selectedCellForPlan.height, state.selectedCellForPlan.width)">
                                <i class="fa fa-scissors"></i> Cut to Size
                            </button>
                            <button type="button" class="btn btn-primary"
                                    t-on-click="assignSelectedCutPlans"
                                    t-att-disabled="state.selectedCutPlanIds.length === 0">
                                <i class="fa fa-check"></i> Assign Selected
                            </button>
                            <button type="button" class="btn btn-secondary" t-on-click="closeCutPlanDialog">
                                <i class="fa fa-times"></i> Cancel
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </t>
</templates>
