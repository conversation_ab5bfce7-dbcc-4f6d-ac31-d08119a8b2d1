<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <!-- Cut Plan SVG Renderer Template -->
    <t t-name="CutPlanSvgRenderer">
        <div class="cut-plan-svg-renderer">
            <div class="svg-container" style="width: 100%; height: 400px; border: 1px solid #ddd; background: #f9f9f9;">
                <div class="text-center p-3">
                    <i class="fas fa-spinner fa-spin"></i> Loading cut plan visualization...
                </div>
            </div>
        </div>
    </t>

    <!-- Cut Plan SVG Widget Template -->
    <t t-name="CutPlanSvgWidget">
        <div class="cut-plan-svg-widget">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Cut Plan Visualization</h5>
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-secondary" data-action="refresh">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                        <button type="button" class="btn btn-outline-secondary" data-action="fullscreen">
                            <i class="fas fa-expand"></i> Fullscreen
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="svg-container" style="width: 100%; min-height: 400px; background: #f9f9f9;">
                        <div class="text-center p-4">
                            <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                            <p class="mt-2 text-muted">Loading visualization...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </t>

    <!-- Cut Plan SVG Component Preview -->
    <t t-name="CutPlanSvgComponentPreview">
        <div class="cut-plan-svg-component-preview">
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">Individual Component</h6>
                        </div>
                        <div class="card-body text-center" style="min-height: 300px;">
                            <div class="individual-preview"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">Combined Preview</h6>
                        </div>
                        <div class="card-body text-center" style="min-height: 300px;">
                            <div class="combined-preview"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </t>

    <!-- Cut Plan SVG Debug Panel -->
    <t t-name="CutPlanSvgDebugPanel">
        <div class="cut-plan-svg-debug-panel">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-bug"></i> SVG Debug Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Plan Values</h6>
                            <div class="plan-values-debug"></div>
                        </div>
                        <div class="col-md-6">
                            <h6>Components Status</h6>
                            <div class="components-status-debug"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </t>

</templates>
