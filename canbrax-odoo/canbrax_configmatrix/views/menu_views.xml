<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- ========================================= -->
    <!-- ACTIONS DEFINITIONS -->
    <!-- ========================================= -->

    <!-- Configurable Products Action -->
    <record id="action_config_matrix_configurable_products" model="ir.actions.act_window">
        <field name="name">Configurable Products</field>
        <field name="res_model">product.template</field>
        <field name="view_mode">list,form</field>
        <field name="domain">[('is_configurable', '=', True)]</field>
        <field name="context">{}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No configurable products found
            </p>
            <p>
                Create products and enable the "Configurable Product" checkbox to see them here.
            </p>
        </field>
    </record>

    <!-- Fixed Price Table Action -->
    <record id="action_config_matrix_operation_price" model="ir.actions.act_window">
        <field name="name">Fixed Price Table</field>
        <field name="res_model">config.matrix.operation.price</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_active': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No operation prices found
            </p>
            <p>
                Create fixed price entries for operations to standardize pricing across your configuration templates.
            </p>
        </field>
    </record>

    <!-- ========================================= -->
    <!-- MENU STRUCTURE - OPTION A: BUSINESS PROCESS FOCUSED -->
    <!-- ========================================= -->

    <!-- Main Menu -->
    <menuitem id="menu_config_matrix_root"
              name="Matrix"
              web_icon="canbrax_configmatrix,static/src/img/matrix.png"
              sequence="50"/>

    <!-- ========================================= -->
    <!-- DECISION TREES - Main product configuration logic -->
    <!-- ========================================= -->
    <menuitem id="menu_config_matrix_decision_trees"
              name="Decision Trees"
              parent="menu_config_matrix_root"
              sequence="10"/>

    <menuitem id="menu_config_matrix_templates"
              name="Templates"
              parent="menu_config_matrix_decision_trees"
              action="action_config_matrix_template"
              sequence="10"/>

    <menuitem id="menu_config_matrix_categories"
              name="Categories"
              parent="menu_config_matrix_decision_trees"
              action="action_config_matrix_category"
              sequence="20"/>

    <menuitem id="menu_config_matrix_import_export_tools"
              name="Import/Export Templates"
              parent="menu_config_matrix_decision_trees"
              action="action_config_matrix_import_export_tools"
              sequence="30"/>

    <!-- ========================================= -->
    <!-- PRODUCTS - Unified product management -->
    <!-- ========================================= -->
    <menuitem id="menu_config_matrix_products"
              name="Products"
              parent="menu_config_matrix_root"
              sequence="20"/>

    <menuitem id="menu_config_matrix_configurable_products"
              name="Configurable Products"
              parent="menu_config_matrix_products"
              action="action_config_matrix_configurable_products"
              sequence="10"/>

    <menuitem id="menu_config_matrix_configurations"
              name="Product Configuration"
              parent="menu_config_matrix_products"
              action="action_config_matrix_configuration"
              sequence="20"/>

    <menuitem id="menu_mesh_products"
              name="Mesh Products"
              parent="menu_config_matrix_products"
              action="action_mesh_products"
              sequence="30"/>

    <!-- ========================================= -->
    <!-- MANUFACTURING - Production-focused -->
    <!-- ========================================= -->
    <menuitem id="menu_config_matrix_manufacturing"
              name="Manufacturing"
              parent="menu_config_matrix_root"
              sequence="30"/>

    <menuitem id="menu_config_matrix_operation_templates"
              name="Operation Templates"
              parent="menu_config_matrix_manufacturing"
              action="action_config_matrix_operation_template"
              sequence="10"/>

    <menuitem id="menu_config_matrix_operation_mapping"
              name="Operation Mapping"
              parent="menu_config_matrix_manufacturing"
              action="action_config_matrix_operation_mapping"
              sequence="20"/>

    <!-- Work Centers Settings (Matrix Settings moved here) -->
    <menuitem id="menu_config_matrix_settings"
              name="Work Centers (Settings)"
              parent="menu_config_matrix_manufacturing"
              action="action_config_matrix_settings_server"
              sequence="30"/>

    <!-- BOMs and Routing (consolidated) -->
    <menuitem id="menu_config_matrix_configurations_root"
              name="BOMs &amp; Routing"
              parent="menu_config_matrix_manufacturing"
              sequence="40"/>

    <menuitem id="menu_config_matrix_template_matrix_assignment"
              name="Template Matrix Assignment"
              parent="menu_config_matrix_configurations_root"
              action="action_config_matrix_template_matrix_assignment"
              sequence="10"/>

    <!-- ========================================= -->
    <!-- PRICING & COSTING - Financial aspects -->
    <!-- ========================================= -->
    <menuitem id="menu_config_matrix_pricing"
              name="Pricing &amp; Costing"
              parent="menu_config_matrix_root"
              sequence="40"/>

    <menuitem id="menu_config_matrix_price_matrices"
              name="Pricing Matrices"
              parent="menu_config_matrix_pricing"
              action="action_config_matrix_price_matrix"
              sequence="10"/>

    <menuitem id="menu_config_matrix_labor_time_matrices"
              name="Labour Matrices"
              parent="menu_config_matrix_pricing"
              action="action_config_matrix_labor_time_matrix"
              sequence="20"/>

    <menuitem id="menu_config_matrix_operation_price"
              name="Fixed Price Tables"
              parent="menu_config_matrix_pricing"
              action="action_config_matrix_operation_price"
              sequence="30"/>

    <!-- ========================================= -->
    <!-- MESH OPERATIONS - Specialized mesh workflow -->
    <!-- ========================================= -->
    <menuitem id="menu_mesh_operations_root"
              name="Mesh Operations"
              parent="menu_config_matrix_root"
              sequence="50"/>

    <menuitem id="menu_mesh_cut_operations"
              name="Cut Operations"
              parent="menu_mesh_operations_root"
              action="action_mesh_cut_operation"
              sequence="10"/>

    <menuitem id="menu_mesh_cut_matrices"
              name="Cut Matrices"
              parent="menu_mesh_operations_root"
              action="action_mesh_cut_matrix"
              sequence="20"/>

    <menuitem id="menu_mesh_cut_plans"
              name="Cut Plans"
              parent="menu_mesh_operations_root"
              action="action_mesh_cut_plan"
              sequence="30"/>

    <!-- ========================================= -->
    <!-- SYSTEM TOOLS - Admin/maintenance tools -->
    <!-- ========================================= -->
    <menuitem id="menu_config_matrix_tools"
              name="System Tools"
              parent="menu_config_matrix_root"
              sequence="90"/>

    <menuitem id="menu_config_matrix_json_import"
              name="Import Templates"
              parent="menu_config_matrix_tools"
              action="action_config_matrix_json_import"
              sequence="10"/>

    <!-- Operation Migration Wizard Action -->
    <record id="action_config_matrix_operation_migration_wizard" model="ir.actions.act_window">
        <field name="name">Migrate Operation Templates</field>
        <field name="res_model">config.matrix.operation.migration.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Migrate Operation Templates
            </p>
            <p>
                This wizard helps migrate operation templates to separate duration and cost calculations.
            </p>
        </field>
    </record>

    <menuitem id="menu_config_matrix_operation_migration"
              name="Data Migration"
              parent="menu_config_matrix_tools"
              action="action_config_matrix_operation_migration_wizard"
              sequence="20"/>

    <!-- Simple Test Menu - Temporarily disabled -->
    <!-- <menuitem id="menu_config_matrix_simple_test"
              name="Simple Matrix Test"
              parent="menu_config_matrix_import_tools"
              action="action_config_matrix_simple_test_wizard"
              sequence="35"/> -->

</odoo>
