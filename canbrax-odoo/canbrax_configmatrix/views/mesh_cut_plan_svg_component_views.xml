<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- SVG Component Form View -->
    <record id="view_mesh_cut_plan_svg_component_form" model="ir.ui.view">
        <field name="name">mesh.cut.plan.svg.component.form</field>
        <field name="model">mesh.cut.plan.svg.component</field>
        <field name="arch" type="xml">
            <form string="Cut Plan SVG Component">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Component Name"/>
                        </h1>
                    </div>
                    
                    <group>
                        <group>
                            <field name="cut_plan_id"/>
                            <field name="component_type"/>
                            <field name="z_index"/>
                        </group>
                        <group>
                            <field name="condition" placeholder="e.g., master_width > 1000"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="SVG Content" name="svg_content">
                            <field name="svg_content" widget="ace" options="{'mode': 'xml'}" 
                                   placeholder="Enter SVG markup here. Use ${field_name} for dynamic values."/>
                        </page>
                        
                        <page string="Preview" name="preview">
                            <div class="row">
                                <div class="col-md-6">
                                    <h4>Individual Component Preview</h4>
                                    <div style="border: 1px solid #ddd; padding: 10px; min-height: 300px;">
                                        <field name="svg_preview" nolabel="1"/>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h4>Combined Preview</h4>
                                    <div style="border: 1px solid #ddd; padding: 10px; min-height: 300px;">
                                        <field name="combined_preview" nolabel="1"/>
                                    </div>
                                </div>
                            </div>
                        </page>
                        
                        <page string="Help" name="help">
                            <div class="alert alert-info">
                                <h4>SVG Component Help</h4>
                                <p><strong>Component Types:</strong></p>
                                <ul>
                                    <li><strong>Base SVG:</strong> The background/foundation SVG that other layers are added to</li>
                                    <li><strong>SVG Layer:</strong> Conditional layers that are added based on conditions</li>
                                </ul>
                                
                                <p><strong>Dynamic Values:</strong></p>
                                <p>Use <code>${field_name}</code> syntax to insert values from the cut plan:</p>
                                <ul>
                                    <li><code>${master_width}</code> - Master sheet width</li>
                                    <li><code>${master_height}</code> - Master sheet height</li>
                                    <li><code>${name}</code> - Cut plan name</li>
                                </ul>
                                
                                <p><strong>Conditions:</strong></p>
                                <p>Use JavaScript expressions to control when layers are shown:</p>
                                <ul>
                                    <li><code>master_width > 1000</code> - Show only for wide sheets</li>
                                    <li><code>master_height >= 620</code> - Show for tall sheets</li>
                                    <li><code>master_width > 1000 &amp;&amp; master_height > 600</code> - Combined conditions</li>
                                </ul>
                                
                                <p><strong>Z-Index:</strong></p>
                                <p>Controls the stacking order. Higher numbers appear on top.</p>
                            </div>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- SVG Component List View -->
    <record id="view_mesh_cut_plan_svg_component_list" model="ir.ui.view">
        <field name="name">mesh.cut.plan.svg.component.list</field>
        <field name="model">mesh.cut.plan.svg.component</field>
        <field name="arch" type="xml">
            <list string="Cut Plan SVG Components">
                <field name="name"/>
                <field name="cut_plan_id"/>
                <field name="component_type"/>
                <field name="z_index"/>
                <field name="condition"/>
            </list>
        </field>
    </record>

    <!-- SVG Component Preview Form View -->
    <record id="view_mesh_cut_plan_svg_component_preview_form" model="ir.ui.view">
        <field name="name">mesh.cut.plan.svg.component.preview.form</field>
        <field name="model">mesh.cut.plan.svg.component</field>
        <field name="arch" type="xml">
            <form string="SVG Component Preview">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                        <h2>
                            <field name="cut_plan_id" readonly="1"/>
                        </h2>
                    </div>
                    
                    <group>
                        <group>
                            <field name="component_type" readonly="1"/>
                            <field name="z_index" readonly="1"/>
                        </group>
                        <group>
                            <field name="condition" readonly="1"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="Individual Preview" name="individual">
                            <div style="text-align: center; padding: 20px;">
                                <field name="svg_preview" nolabel="1"/>
                            </div>
                        </page>
                        
                        <page string="Combined Preview" name="combined">
                            <div style="text-align: center; padding: 20px;">
                                <field name="combined_preview" nolabel="1"/>
                            </div>
                        </page>
                    </notebook>
                </sheet>
                <footer>
                    <button string="Close" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Actions -->
    <record id="action_mesh_cut_plan_svg_component" model="ir.actions.act_window">
        <field name="name">Cut Plan SVG Components</field>
        <field name="res_model">mesh.cut.plan.svg.component</field>
        <field name="view_mode">list,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first Cut Plan SVG Component!
            </p>
            <p>
                SVG Components allow you to create dynamic visualizations for cut plans.
                Create a base component and add conditional layers to show different cutting scenarios.
            </p>
        </field>
    </record>



</odoo>
