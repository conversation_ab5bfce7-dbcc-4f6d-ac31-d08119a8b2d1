# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError

class SampleCutOperationsWizard(models.TransientModel):
    _name = 'sample.cut.operations.wizard'
    _description = 'Create Sample Cut Operations'
    
    mesh_series = fields.Selection([
        ('saltwater', 'Saltwater Series'),
        ('basix', 'Basix Series'),
        ('diamond', 'Diamond Grill'),
        ('flyscreen', 'Fly Screen'),
    ], string='Mesh Series', required=True, default='saltwater')
    
    operation_count = fields.Integer('Number of Operations', default=10, required=True)
    
    def action_create_sample_operations(self):
        """Create sample cut operations with various dimensions"""
        self.ensure_one()
        
        # Sample dimensions that will test different scenarios
        sample_dimensions = [
            # Small pieces (should find unplanned off-cuts)
            (300, 250, "Small Window"),
            (350, 280, "Small Door Panel"),
            (400, 300, "Medium Window"),
            
            # Medium pieces (should find planned off-cuts)
            (480, 280, "Medium Window"),
            (500, 350, "Standard Door Panel"),
            (600, 400, "Large Window"),
            
            # Large pieces (should find master sheets)
            (800, 600, "Large Door"),
            (1000, 800, "Extra Large Door"),
            (900, 700, "Sliding Door Panel"),
            (1100, 600, "Wide Door"),
            
            # Edge cases
            (450, 380, "Custom Size 1"),
            (550, 420, "Custom Size 2"),
            (750, 500, "Custom Size 3"),
            (320, 280, "Small Custom"),
            (1200, 900, "Oversized Panel"),
        ]
        
        created_operations = []
        
        for i, (width, height, description) in enumerate(sample_dimensions[:self.operation_count]):
            # Create the operation
            operation_vals = {
                'name': f"Sample {i+1:02d} - {description} {width}x{height}mm",
                'required_width': width,
                'required_height': height,
                'required_qty': 1.0,
                'mesh_series': self.mesh_series,
                'state': 'draft',
            }
            
            operation = self.env['mesh.cut.operation'].create(operation_vals)
            
            # Try to find mesh for this operation
            try:
                operation.action_find_mesh()
                created_operations.append(operation)
            except UserError:
                # If no mesh found, keep the operation in draft state
                created_operations.append(operation)
        
        # Return action to view created operations
        return {
            'name': 'Sample Cut Operations',
            'type': 'ir.actions.act_window',
            'res_model': 'mesh.cut.operation',
            'view_mode': 'list,form',
            'domain': [('id', 'in', [op.id for op in created_operations])],
            'context': {'create': False},
        }

    def action_find_mesh_for_all_samples(self):
        """Find mesh for all existing sample operations"""
        self.ensure_one()

        # Find all sample operations
        sample_operations = self.env['mesh.cut.operation'].search([
            ('name', 'ilike', 'Sample %'),
            ('state', '=', 'draft'),
            ('source_product_id', '=', False),
        ])

        success_count = 0
        for operation in sample_operations:
            try:
                operation.action_find_mesh()
                success_count += 1
            except UserError:
                # Skip operations where no mesh is found
                continue

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Mesh Assignment Complete',
                'message': f'Successfully assigned mesh to {success_count} out of {len(sample_operations)} sample operations.',
                'type': 'success',
                'sticky': False,
            }
        }
