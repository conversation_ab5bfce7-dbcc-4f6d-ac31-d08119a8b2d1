<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Sample Cut Operations Wizard Form -->
    <record id="view_sample_cut_operations_wizard_form" model="ir.ui.view">
        <field name="name">sample.cut.operations.wizard.form</field>
        <field name="model">sample.cut.operations.wizard</field>
        <field name="arch" type="xml">
            <form string="Create Sample Cut Operations">
                <sheet>
                    <div class="oe_title">
                        <h1>Create Sample Cut Operations</h1>
                        <p>Generate sample cut operations to test the mesh finding system.</p>
                    </div>
                    
                    <group>
                        <field name="mesh_series"/>
                        <field name="operation_count"/>
                    </group>
                    
                    <div class="alert alert-info">
                        <p><strong>This will create sample operations with various dimensions:</strong></p>
                        <ul>
                            <li>Small pieces (300x250mm, 350x280mm, etc.) - should find unplanned off-cuts</li>
                            <li>Medium pieces (480x280mm, 500x350mm, etc.) - should find planned off-cuts</li>
                            <li>Large pieces (800x600mm, 1000x800mm, etc.) - should find master sheets</li>
                        </ul>
                    </div>
                </sheet>
                
                <footer>
                    <button name="action_create_sample_operations" type="object"
                            string="Create Sample Operations" class="btn-primary"/>
                    <button name="action_find_mesh_for_all_samples" type="object"
                            string="Find Mesh for Existing Samples" class="btn-secondary"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Action for Sample Cut Operations Wizard -->
    <record id="action_sample_cut_operations_wizard" model="ir.actions.act_window">
        <field name="name">Create Sample Cut Operations</field>
        <field name="res_model">sample.cut.operations.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>

    <!-- Menu item -->
    <menuitem id="menu_sample_cut_operations_wizard"
              name="Create Sample Operations"
              parent="menu_mesh_operations_root"
              action="action_sample_cut_operations_wizard"
              sequence="40"/>
</odoo>
