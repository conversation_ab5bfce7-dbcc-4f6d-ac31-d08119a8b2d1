<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Test Mesh System Wizard Form View -->
        <record id="view_test_mesh_system_wizard_form" model="ir.ui.view">
            <field name="name">test.mesh.system.wizard.form</field>
            <field name="model">test.mesh.system.wizard</field>
            <field name="arch" type="xml">
                <form string="Test Mesh System">
                    <sheet>
                        <div class="oe_title">
                            <h1>Test Mesh System</h1>
                            <p>This wizard tests the mesh cut operation system with sample data</p>
                        </div>
                        
                        <group>
                            <group string="Test Parameters">
                                <field name="config_id" placeholder="Select existing Draft BOM configuration"
                                       domain="[('state', '=', 'draft')]"/>
                                <separator string="OR use template with test data"/>
                                <field name="template_id"/>
                                <field name="mesh_width"/>
                                <field name="mesh_height"/>
                            </group>
                        </group>
                        
                        <group string="Test Results" invisible="not test_results">
                            <field name="test_results" nolabel="1" widget="text" readonly="1"/>
                        </group>
                    </sheet>
                    
                    <footer>
                        <button name="action_test_mesh_system" string="Run Test" type="object" class="btn-primary"/>
                        <button string="Cancel" class="btn-secondary" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>
        
        <!-- Action to open the wizard -->
        <record id="action_test_mesh_system_wizard" model="ir.actions.act_window">
            <field name="name">Test Mesh System</field>
            <field name="res_model">test.mesh.system.wizard</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
        </record>
        
        <!-- Menu item -->
        <menuitem id="menu_test_mesh_system"
                  name="Test Mesh System"
                  parent="menu_mesh_operations_root"
                  action="action_test_mesh_system_wizard"
                  sequence="999"/>
        
    </data>
</odoo>
